{"name": "invoice", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build", "preview": "vite preview"}, "dependencies": {"@fortawesome/fontawesome-free": "^6.7.2", "element-plus": "^2.10.2", "pinia": "^3.0.3", "vue": "^3.5.13", "vue-router": "^4.5.1", "vuex": "^4.1.0"}, "devDependencies": {"@types/node": "^24.0.4", "@vitejs/plugin-vue": "^5.2.3", "@vue/tsconfig": "^0.7.0", "naive-ui": "^2.42.0", "sass": "^1.89.2", "typescript": "~5.8.3", "unplugin-auto-import": "^19.3.0", "unplugin-vue-components": "^28.7.0", "vite": "^6.3.5", "vue-tsc": "^2.2.8"}}