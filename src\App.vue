<template>
  <div class="app-container">
    <h1>NumberRoller Demo</h1>

    <NumberRoller
      ref="numberRollerRef"
      :rollerConfig="{ rollerCount: 3, codeCount: 4 }"
    />

    <!-- 显示监听到的结果 -->
    <div class="results-section" v-if="rollerResults.length > 0">
      <h2>监听结果：</h2>
      <div class="result-item" v-for="item in rollerResults" :key="item.index">
        <strong>Roller {{ item.index }}:</strong>
        <span v-if="item.recordList.length > 0">
          {{ item.recordList.join(", ") }}
        </span>
        <span v-else class="no-records">暂无记录</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted } from "vue";
import NumberRoller from "@/components/Calendar/NumberRoller.vue";
import type { ProcessedRollerItem } from "@/components/Calendar/types";

// 创建变量存储监听结果
const rollerResults = ref<ProcessedRollerItem[]>([]);

// NumberRoller 组件引用
const numberRollerRef = ref<InstanceType<typeof NumberRoller>>();

// 组件挂载后开始监听
onMounted(() => {
  if (numberRollerRef.value) {
    // 方法2：监听数据变化
    watch(
      () => numberRollerRef.value?.processedRollerList,
      (newResults: ProcessedRollerItem[] | undefined) => {
        console.log("监听到结果更新:", newResults);
        // 将监听结果赋值给变量
        if (newResults) {
          rollerResults.value = newResults;
        }
      },
      {
        deep: true,
        immediate: true, // 立即执行一次，获取初始值
      }
    );
  }
});
</script>

<style scoped>
.app-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

h1 {
  text-align: center;
  color: #333;
  margin-bottom: 30px;
}

.results-section {
  margin-top: 40px;
  padding: 20px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  border-radius: 15px;
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.results-section h2 {
  color: #333;
  margin-bottom: 20px;
  font-size: 20px;
}

.result-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px 15px;
  margin-bottom: 10px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 8px;
  border-left: 4px solid #667eea;
}

.result-item strong {
  color: #667eea;
  min-width: 80px;
}

.no-records {
  color: #999;
  font-style: italic;
}
</style>
