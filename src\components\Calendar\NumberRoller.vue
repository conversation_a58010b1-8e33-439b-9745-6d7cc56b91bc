<template>
  <div class="roller-container">
    <div class="roller">
      <!-- 每个滚轮组 -->
      <div
        class="roller-group"
        v-for="rollerItem in defaultConfig.rollerItemList"
        :key="rollerItem.index"
      >
        <!-- 左侧：数字滚轮区域 -->
        <div class="code-section">
          <div
            class="code-item"
            v-for="codeIndex in defaultConfig.codeCount"
            :key="codeIndex"
            :class="{ 'first-code': codeIndex === 1 }"
          >
            <div class="number-display">
              {{ getCurrentNumber(rollerItem.index - 1, codeIndex - 1) }}
            </div>
          </div>
        </div>

        <!-- 右侧：按钮组 -->
        <div class="button-section">
          <el-button
            @click="lockRoller(rollerItem.index - 1)"
            :type="rollerItem.isLocked ? 'success' : 'primary'"
            :disabled="isRolling"
            size="small"
          >
            {{ rollerItem.isLocked ? "Locked" : "Lock" }}
          </el-button>
          <el-button
            @click="resetSingleRoller(rollerItem.index - 1)"
            type="default"
            :disabled="isRolling || rollerItem.isLocked"
            size="small"
          >
            Reset
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from "vue";
import { initRollerConfig, type RollerConfig } from "./types";
import { getRandomNumber, getUniqueRandomNumberList } from "@/utils/RandomUtil";

const props = defineProps<{
  rollerConfig?: Partial<RollerConfig>;
}>();

const defaultConfig = ref<RollerConfig>(
  initRollerConfig(props.rollerConfig ?? {})
);

// 每个滚轮组的每个code-item的当前数字索引 [rollerIndex][codeIndex]
const currentNumberIndices = reactive<number[][]>(
  Array(defaultConfig.value.rollerCount)
    .fill(null)
    .map(() => Array(defaultConfig.value.codeCount).fill(0))
);

// 滚动状态
const isRolling = ref(false);

// 动画状态 [rollerIndex][codeIndex]
const animatingNumbers = reactive<boolean[][]>(
  Array(defaultConfig.value.rollerCount)
    .fill(null)
    .map(() => Array(defaultConfig.value.codeCount).fill(false))
);

// 动画显示的数字 [rollerIndex][codeIndex]
const displayNumbers = reactive<number[][]>(
  Array(defaultConfig.value.rollerCount)
    .fill(null)
    .map((_, rollerIndex) =>
      Array(defaultConfig.value.codeCount)
        .fill(null)
        .map(
          (_, codeIndex) =>
            defaultConfig.value.rollerItemList![rollerIndex].codeList![
              currentNumberIndices[rollerIndex][codeIndex]
            ]
        )
    )
);

// 获取当前显示的数字
const getCurrentNumber = (rollerIndex: number, codeIndex: number) => {
  if (animatingNumbers[rollerIndex][codeIndex]) {
    return displayNumbers[rollerIndex][codeIndex];
  }

  const rollerItem = defaultConfig.value.rollerItemList![rollerIndex];
  const currentIndex = currentNumberIndices[rollerIndex][codeIndex];
  return rollerItem.codeList![currentIndex];
};

// 锁定滚轮并记录数字
const lockRoller = (rollerIndex: number) => {
  if (isRolling.value) return;

  const rollerItem = defaultConfig.value.rollerItemList![rollerIndex];

  if (rollerItem.isLocked) {
    // 解锁
    rollerItem.isLocked = false;
    console.log(`Roller ${rollerIndex + 1} unlocked`);
  } else {
    // 锁定并记录当前选中的数字
    rollerItem.isLocked = true;
    recordCurrentNumbers(rollerIndex);
  }
};

// 记录当前选中的数字
const recordCurrentNumbers = (rollerIndex: number) => {
  const rollerItem = defaultConfig.value.rollerItemList![rollerIndex];
  if (!rollerItem.codeList?.length) return;

  const currentNumbers: number[] = [];

  // 获取每个code-item当前选中的数字
  for (
    let codeIndex = 0;
    codeIndex < defaultConfig.value.codeCount!;
    codeIndex++
  ) {
    const currentIndex = currentNumberIndices[rollerIndex][codeIndex];
    currentNumbers.push(rollerItem.codeList[currentIndex]);
  }

  // 添加到记录列表
  if (!rollerItem.recordList) {
    rollerItem.recordList = [];
  }
  rollerItem.recordList.push(...currentNumbers);

  console.log(`Roller ${rollerIndex + 1} locked with numbers:`, currentNumbers);
  console.log(`Roller ${rollerIndex + 1} record list:`, rollerItem.recordList);
};

// 重置单个滚轮
const resetSingleRoller = async (rollerIndex: number) => {
  if (isRolling.value) return;

  const rollerItem = defaultConfig.value.rollerItemList![rollerIndex];
  if (rollerItem.isLocked) return;

  isRolling.value = true;

  // 重新生成 codeList
  const minCode = defaultConfig.value.minCode!;
  const maxCode = defaultConfig.value.maxCode!;
  const codeCount = defaultConfig.value.codeCount!;

  rollerItem.codeList = getUniqueRandomNumberList(minCode, maxCode, codeCount);
  console.log(`Roller ${rollerIndex + 1} new codeList:`, rollerItem.codeList);

  // 开始滚动动画
  await startRollingAnimation(rollerIndex);

  isRolling.value = false;
};

// 开始滚动动画
const startRollingAnimation = async (rollerIndex: number) => {
  const rollerItem = defaultConfig.value.rollerItemList![rollerIndex];
  const animationDuration = 2000; // 2秒动画
  const frameRate = 60; // 60fps
  const totalFrames = (animationDuration / 1000) * frameRate;

  // 为每个 code-item 开始动画
  const animationPromises = [];

  for (
    let codeIndex = 0;
    codeIndex < defaultConfig.value.codeCount!;
    codeIndex++
  ) {
    animatingNumbers[rollerIndex][codeIndex] = true;

    const promise = new Promise<void>((resolve) => {
      let frame = 0;
      const targetIndex = getRandomNumber(0, rollerItem.codeList!.length - 1);

      const animate = () => {
        if (frame < totalFrames) {
          // 随机显示数字创造滚动效果
          const randomIndex = getRandomNumber(
            0,
            rollerItem.codeList!.length - 1
          );
          displayNumbers[rollerIndex][codeIndex] =
            rollerItem.codeList![randomIndex];

          frame++;
          requestAnimationFrame(animate);
        } else {
          // 动画结束，设置最终数字
          currentNumberIndices[rollerIndex][codeIndex] = targetIndex;
          displayNumbers[rollerIndex][codeIndex] =
            rollerItem.codeList![targetIndex];
          animatingNumbers[rollerIndex][codeIndex] = false;
          resolve();
        }
      };

      animate();
    });

    animationPromises.push(promise);
  }

  await Promise.all(animationPromises);
};

console.log(defaultConfig.value);
</script>

<style scoped lang="scss">
.roller-container {
  width: 100%;
  max-width: 1200px;
  min-height: 600px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 40px 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);

  .roller {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 30px;
    width: 100%;

    .roller-group {
      display: flex;
      flex-direction: row;
      align-items: center;
      gap: 30px;
      padding: 20px;
      background: rgba(255, 255, 255, 0.1);
      border-radius: 15px;
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.2);
      min-width: 400px;
      justify-content: space-between;

      .code-section {
        display: flex;
        gap: 15px;
        align-items: center;
        justify-content: center;
        flex-wrap: wrap;

        .code-item {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 80px;
          height: 80px;
          background: linear-gradient(145deg, #ffffff, #f0f0f0);
          border: 2px solid #fff;
          border-radius: 12px;
          box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1),
            inset 0 1px 0 rgba(255, 255, 255, 0.8);
          transition: all 0.3s ease;

          &.first-code {
            background: linear-gradient(145deg, #ffd700, #ffed4e);
            border-color: #ffb700;
            box-shadow: 0 4px 12px rgba(255, 215, 0, 0.3),
              inset 0 1px 0 rgba(255, 255, 255, 0.8);
            transform: scale(1.1);
          }

          .number-display {
            font-size: 24px;
            font-weight: bold;
            color: #333;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
            transition: all 0.2s ease;
          }

          &.first-code .number-display {
            font-size: 26px;
            color: #b8860b;
            text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.2);
          }
        }
      }

      .button-section {
        display: flex;
        flex-direction: column;
        gap: 10px;
        align-items: center;
        justify-content: center;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .roller-container {
    padding: 20px 10px;
    min-height: 500px;
    max-width: 100%;

    .roller {
      gap: 20px;

      .roller-group {
        flex-direction: column;
        min-width: 280px;
        padding: 15px;
        gap: 20px;

        .code-section {
          gap: 10px;

          .code-item {
            width: 60px;
            height: 60px;

            .number-display {
              font-size: 20px;
            }

            &.first-code {
              transform: scale(1.05);

              .number-display {
                font-size: 22px;
              }
            }
          }
        }

        .button-section {
          flex-direction: row;
          gap: 15px;
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .roller-container {
    padding: 15px 5px;

    .roller {
      .roller-group {
        min-width: 250px;
        padding: 10px;

        .code-section {
          gap: 8px;

          .code-item {
            width: 50px;
            height: 50px;

            .number-display {
              font-size: 18px;
            }

            &.first-code {
              transform: scale(1.05);

              .number-display {
                font-size: 20px;
              }
            }
          }
        }

        .button-section {
          gap: 10px;
        }
      }
    }
  }
}
</style>
