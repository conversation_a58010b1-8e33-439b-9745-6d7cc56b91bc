<template>
  <div class="roller-container">
    <div class="roller">
      <!-- Each roller group -->
      <div
        class="roller-group"
        v-for="rollerItem in defaultConfig.rollerItemList"
        :key="rollerItem.index"
      >
        <!-- Left side: Number roller area -->
        <div class="code-section">
          <div
            class="code-item"
            v-for="codeIndex in defaultConfig.codeCount"
            :key="codeIndex"
            :class="{
              'first-code': codeIndex === 1,
              rolling: animatingNumbers[rollerItem.index - 1][codeIndex - 1],
            }"
          >
            <div class="number-display">
              {{ getCurrentNumber(rollerItem.index - 1, codeIndex - 1) }}
            </div>
          </div>
        </div>

        <!-- Right side: Button group -->
        <div class="button-section">
          <el-button
            @click="lockRoller(rollerItem.index - 1)"
            type="success"
            :disabled="
              rollerStates[rollerItem.index - 1] || rollerItem.isLocked
            "
            size="small"
          >
            Lock
          </el-button>
          <el-button
            @click="resetSingleRoller(rollerItem.index - 1)"
            type="default"
            :disabled="
              rollerStates[rollerItem.index - 1] || rollerItem.isLocked
            "
            size="small"
          >
            重置
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from "vue";
import {
  initRollerConfig,
  type RollerConfig,
  type ProcessedRollerItem,
} from "./types";
import { getUniqueRandomNumberList } from "@/utils/RandomUtil";

const props = defineProps<{
  rollerConfig?: Partial<RollerConfig>;
}>();

const defaultConfig = ref<RollerConfig>(
  initRollerConfig(props.rollerConfig ?? {})
);

// Current number index for each code-item in each roller group [rollerIndex][codeIndex]
const currentNumberIndices = reactive<number[][]>(
  Array(defaultConfig.value.rollerCount)
    .fill(null)
    .map(() => Array(defaultConfig.value.codeCount).fill(0))
);

// Rolling state for each roller [rollerIndex]
const rollerStates = reactive<boolean[]>(
  Array(defaultConfig.value.rollerCount).fill(false)
);

// Animation state [rollerIndex][codeIndex]
const animatingNumbers = reactive<boolean[][]>(
  Array(defaultConfig.value.rollerCount)
    .fill(null)
    .map(() => Array(defaultConfig.value.codeCount).fill(false))
);

// Numbers displayed during animation [rollerIndex][codeIndex]
const displayNumbers = reactive<(number | string)[][]>(
  Array(defaultConfig.value.rollerCount)
    .fill(null)
    .map(
      () => Array(defaultConfig.value.codeCount).fill("?") // Display question mark during initialization
    )
);

// Get currently displayed number
const getCurrentNumber = (rollerIndex: number, codeIndex: number) => {
  // Directly return value from displayNumbers
  // "?" during initialization, animated number during rolling, final number after rolling ends
  return displayNumbers[rollerIndex][codeIndex];
};

// Lock roller and record numbers
const lockRoller = (rollerIndex: number) => {
  if (rollerStates[rollerIndex]) return;

  const rollerItem = defaultConfig.value.rollerItemList![rollerIndex];

  // Only allow locking, no unlocking
  if (!rollerItem.isLocked) {
    rollerItem.isLocked = true;
    recordCurrentNumbers(rollerIndex);
  }
};

// Record currently selected numbers
const recordCurrentNumbers = (rollerIndex: number) => {
  const rollerItem = defaultConfig.value.rollerItemList![rollerIndex];
  if (!rollerItem.codeList?.length) return;

  const currentNumbers: number[] = [];

  // Get currently displayed numbers for each code-item (directly from codeList in order)
  for (
    let codeIndex = 0;
    codeIndex < defaultConfig.value.codeCount!;
    codeIndex++
  ) {
    currentNumbers.push(rollerItem.codeList[codeIndex]);
  }

  // Add to record list
  if (!rollerItem.recordList) {
    rollerItem.recordList = [];
  }
  rollerItem.recordList.push(...currentNumbers);

  console.log(`Roller ${rollerIndex + 1} locked with numbers:`, currentNumbers);
  console.log(`Roller ${rollerIndex + 1} record list:`, rollerItem.recordList);
};

// Reset single roller
const resetSingleRoller = async (rollerIndex: number) => {
  if (rollerStates[rollerIndex]) return;

  const rollerItem = defaultConfig.value.rollerItemList![rollerIndex];
  if (rollerItem.isLocked) return;

  rollerStates[rollerIndex] = true;

  // Regenerate codeList
  const minCode = defaultConfig.value.minCode!;
  const maxCode = defaultConfig.value.maxCode!;
  const codeCount = defaultConfig.value.codeCount!;

  rollerItem.codeList = getUniqueRandomNumberList(minCode, maxCode, codeCount);
  console.log(`Roller ${rollerIndex + 1} new codeList:`, rollerItem.codeList);

  // Start rolling animation
  await startRollingAnimation(rollerIndex);

  rollerStates[rollerIndex] = false;
};

// Start rolling animation (sequential rolling)
const startRollingAnimation = async (rollerIndex: number) => {
  const rollerItem = defaultConfig.value.rollerItemList![rollerIndex];
  const animationDuration = 1000; // 1 second animation per number
  const numberChangeInterval = 20; // Change number every 20ms
  const totalChanges = animationDuration / numberChangeInterval;

  // Start animation for each code-item in sequence
  for (
    let codeIndex = 0;
    codeIndex < defaultConfig.value.codeCount!;
    codeIndex++
  ) {
    await new Promise<void>((resolve) => {
      animatingNumbers[rollerIndex][codeIndex] = true;

      let changeCount = 0;
      // Target number is the corresponding index number in codeList
      const targetNumber = rollerItem.codeList![codeIndex];

      // Get codeSequenceList for rolling display
      const codeSequenceList = defaultConfig.value.codeSequenceList!;
      let currentSequenceIndex = 0;

      const animate = () => {
        if (changeCount < totalChanges) {
          // First 90% time: sequential rolling of codeSequenceList, last 10% time: stabilize to target number
          if (changeCount < totalChanges * 0.9) {
            // Display numbers from codeSequenceList in sequence
            displayNumbers[rollerIndex][codeIndex] =
              codeSequenceList[currentSequenceIndex];

            // Move to next number, return to beginning when reaching end
            currentSequenceIndex =
              (currentSequenceIndex + 1) % codeSequenceList.length;
          } else {
            // Final stage: display target number
            displayNumbers[rollerIndex][codeIndex] = targetNumber;
          }

          changeCount++;
          setTimeout(animate, numberChangeInterval);
        } else {
          // Animation ends, set final number
          currentNumberIndices[rollerIndex][codeIndex] = codeIndex;
          displayNumbers[rollerIndex][codeIndex] = targetNumber;
          animatingNumbers[rollerIndex][codeIndex] = false;
          resolve();
        }
      };

      animate();
    });
  }
};

// Processed rollerItemList containing only index and recordList
const processedRollerList = computed<ProcessedRollerItem[]>(() => {
  const processedList =
    defaultConfig.value.rollerItemList?.map((item) => ({
      index: item.index,
      recordList: item.recordList || [],
    })) || [];

  console.log("Data returned to parent component>>" + processedList);
  return processedList;
});

// Method to get processed data
const getProcessedRollerList = () => {
  return processedRollerList.value;
};

// Expose methods and properties to parent component
defineExpose({
  processedRollerList,
  getProcessedRollerList,
});

console.log(defaultConfig.value);
</script>

<style scoped lang="scss">
.roller-container {
  width: 100%;
  max-width: 800px;
  min-height: auto;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 24px;
  background: #ffffff;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

  .roller {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 16px;
    width: 100%;

    .roller-group {
      display: flex;
      flex-direction: row;
      align-items: center;
      gap: 24px;
      padding: 16px 20px;
      background: #f9fafb;
      border-radius: 8px;
      border: 1px solid #e5e7eb;
      min-width: 320px;
      justify-content: space-between;
      transition: all 0.2s ease;

      .code-section {
        display: flex;
        gap: 12px;
        align-items: center;
        justify-content: center;
        flex-wrap: wrap;

        .code-item {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 48px;
          height: 48px;
          background: #ffffff;
          border: 1px solid #d1d5db;
          border-radius: 6px;
          box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
          transition: all 0.2s ease;
          position: relative;
          overflow: hidden;

          &.first-code {
            background: #3b82f6;
            border-color: #2563eb;
            color: white;

            .number-display {
              color: white;
            }
          }

          .number-display {
            font-size: 16px;
            font-weight: 600;
            color: #374151;
            transition: all 0.15s ease;
            font-family: "SF Mono", "Monaco", "Inconsolata", "Roboto Mono",
              monospace;
          }

          /* Rolling animation effect */
          &.rolling {
            background: #eff6ff;
            border-color: #3b82f6;
            box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);

            .number-display {
              animation: numberPulse 0.15s ease-in-out;
            }
          }

          &:hover {
            border-color: #9ca3af;
          }
        }

        @keyframes numberPulse {
          0% {
            transform: scale(1);
          }
          50% {
            transform: scale(1.05);
          }
          100% {
            transform: scale(1);
          }
        }
      }

      .button-section {
        display: flex;
        flex-direction: row;
        gap: 8px;
        align-items: center;
        justify-content: center;
      }
    }
  }
}

/* Responsive design */
@media (max-width: 768px) {
  .roller-container {
    padding: 16px;
    max-width: 100%;

    .roller {
      gap: 12px;

      .roller-group {
        flex-direction: column;
        min-width: 280px;
        padding: 12px 16px;
        gap: 16px;

        .code-section {
          gap: 8px;

          .code-item {
            width: 40px;
            height: 40px;

            .number-display {
              font-size: 14px;
            }
          }
        }

        .button-section {
          flex-direction: row;
          gap: 8px;
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .roller-container {
    padding: 12px;

    .roller {
      .roller-group {
        min-width: 240px;
        padding: 8px 12px;

        .code-section {
          gap: 6px;

          .code-item {
            width: 36px;
            height: 36px;

            .number-display {
              font-size: 12px;
            }
          }
        }

        .button-section {
          gap: 6px;
        }
      }
    }
  }
}
</style>
