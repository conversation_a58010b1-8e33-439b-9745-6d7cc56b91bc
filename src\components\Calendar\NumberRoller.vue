<template>
  <div class="roller-container">
    <div class="roller">
      <!-- 每个滚轮组 -->
      <div
        class="roller-group"
        v-for="rollerItem in defaultConfig.rollerItemList"
        :key="rollerItem.index"
      >
        <!-- 代码项滚轮区域 -->
        <div class="code-group">
          <ul
            class="code-item"
            v-for="codeIndex in defaultConfig.codeCount"
            :key="codeIndex"
            :style="{
              transform: `rotateX(${getRollerRotation(
                rollerItem.index - 1,
                codeIndex - 1
              )}deg)`,
            }"
          >
            <li
              v-for="(item, index) in rollerItem.codeList"
              :key="index"
              :style="getLiTransform(index, rollerItem.codeList?.length)"
            >
              {{ item }}
            </li>
          </ul>
        </div>

        <!-- 按钮组 -->
        <div class="button-group">
          <el-button
            @click="toggleLock(rollerItem.index - 1)"
            :type="rollerItem.isLocked ? 'success' : 'default'"
            :disabled="isRolling"
            size="small"
          >
            {{ rollerItem.isLocked ? "Locked" : "Lock" }}
          </el-button>
        </div>

        <!-- 记录显示 -->
        <div
          class="record-display"
          v-if="rollerItem.recordList && rollerItem.recordList.length > 0"
        >
          <div class="record-title">Records:</div>
          <div class="record-numbers">
            <span
              v-for="(record, index) in rollerItem.recordList"
              :key="index"
              class="record-number"
            >
              {{ record }}
            </span>
          </div>
        </div>
      </div>

      <!-- 全局控制按钮 -->
      <div class="global-controls">
        <el-button
          @click="rollAll"
          type="primary"
          :loading="isRolling"
          size="large"
        >
          {{ isRolling ? "Rolling..." : "Roll All Unlocked" }}
        </el-button>
        <el-button
          @click="resetAll"
          type="default"
          :disabled="isRolling"
          size="large"
        >
          Reset All
        </el-button>
        <el-button
          @click="clearRecords"
          type="warning"
          :disabled="isRolling"
          size="large"
        >
          Clear Records
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from "vue";
import { initRollerConfig, type RollerConfig, type RollerItem } from "./types";
import { getRandomNumber } from "@/utils/RandomUtil";

const props = defineProps<{
  rollerConfig?: Partial<RollerConfig>;
}>();

const defaultConfig = ref<RollerConfig>(
  initRollerConfig(props.rollerConfig ?? {})
);

// 每个滚轮组的每个code-item的旋转角度 [rollerIndex][codeIndex]
const rollerRotations = reactive<number[][]>(
  Array(defaultConfig.value.rollerCount)
    .fill(null)
    .map(() => Array(defaultConfig.value.codeCount).fill(0))
);

// 滚动状态
const isRolling = ref(false);

// 计算每个 li 元素的 3D 变换
const getLiTransform = (index: number, length: number | undefined) => {
  if (!length) {
    return {};
  }

  const averageDeg = 360 / length;
  const rotateX = index * averageDeg;
  const rotateZ = 80 / 2 / Math.tan((averageDeg / 2 / 180) * Math.PI);
  return {
    transform: `rotateX(-${rotateX}deg) translateZ(${rotateZ}px)`,
  };
};

// 获取特定滚轮的特定code-item的旋转角度
const getRollerRotation = (rollerIndex: number, codeIndex: number) => {
  return rollerRotations[rollerIndex]?.[codeIndex] || 0;
};

// 切换锁定状态
const toggleLock = (rollerIndex: number) => {
  if (isRolling.value) return;

  const rollerItem = defaultConfig.value.rollerItemList![rollerIndex];

  if (rollerItem.isLocked) {
    // 解锁
    rollerItem.isLocked = false;
  } else {
    // 锁定并记录当前选中的数字
    rollerItem.isLocked = true;
    recordCurrentNumbers(rollerIndex);
  }
};

// 记录当前选中的数字
const recordCurrentNumbers = (rollerIndex: number) => {
  const rollerItem = defaultConfig.value.rollerItemList![rollerIndex];
  if (!rollerItem.codeList?.length) return;

  const currentNumbers: number[] = [];

  // 获取每个code-item当前选中的数字
  for (
    let codeIndex = 0;
    codeIndex < defaultConfig.value.codeCount!;
    codeIndex++
  ) {
    const rotation = rollerRotations[rollerIndex][codeIndex];
    const itemCount = rollerItem.codeList.length;
    const averageDeg = 360 / itemCount;

    // 计算当前旋转角度对应的索引
    const normalizedRotation = ((rotation % 360) + 360) % 360;
    const index = Math.round(normalizedRotation / averageDeg) % itemCount;
    currentNumbers.push(rollerItem.codeList[index]);
  }

  // 添加到记录列表
  if (!rollerItem.recordList) {
    rollerItem.recordList = [];
  }
  rollerItem.recordList.push(...currentNumbers);
};

// 滚动单个滚轮的所有code-item
const rollSingleRoller = (rollerIndex: number) => {
  const rollerItem = defaultConfig.value.rollerItemList![rollerIndex];
  if (rollerItem.isLocked || !rollerItem.codeList?.length) return;

  const itemCount = rollerItem.codeList.length;
  const averageDeg = 360 / itemCount;

  // 滚动该滚轮的所有code-item
  for (
    let codeIndex = 0;
    codeIndex < defaultConfig.value.codeCount!;
    codeIndex++
  ) {
    const randomIndex = getRandomNumber(0, itemCount - 1);
    const targetRotation = randomIndex * averageDeg;

    // 添加多圈旋转效果
    const extraRotations = getRandomNumber(3, 6) * 360;
    rollerRotations[rollerIndex][codeIndex] = targetRotation + extraRotations;
  }
};

// 滚动所有未锁定的滚轮
const rollAll = async () => {
  if (isRolling.value) return;

  isRolling.value = true;

  // 创建所有滚动的 Promise
  const rollPromises = [];
  for (let i = 0; i < defaultConfig.value.rollerCount!; i++) {
    const rollerItem = defaultConfig.value.rollerItemList![i];
    if (!rollerItem.isLocked) {
      rollPromises.push(
        new Promise<void>((resolve) => {
          setTimeout(() => {
            rollSingleRoller(i);
            // 等待动画完成
            setTimeout(resolve, 2000);
          }, i * 200);
        })
      );
    }
  }

  // 等待所有滚动完成
  await Promise.all(rollPromises);
  isRolling.value = false;
};

// 重置所有滚轮
const resetAll = () => {
  if (isRolling.value) return;

  // 重置旋转角度
  rollerRotations.forEach((rollerRotation, rollerIndex) => {
    rollerRotation.forEach((_, codeIndex) => {
      rollerRotations[rollerIndex][codeIndex] = 0;
    });
  });

  // 重置锁定状态
  defaultConfig.value.rollerItemList?.forEach((rollerItem) => {
    rollerItem.isLocked = false;
  });
};

// 清空所有记录
const clearRecords = () => {
  if (isRolling.value) return;

  defaultConfig.value.rollerItemList?.forEach((rollerItem) => {
    rollerItem.recordList = [];
  });
};
</script>

<style scoped lang="scss">
.roller-container {
  width: 100%;
  max-width: 1200px;
  min-height: 600px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 40px 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);

  .roller {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 30px;
    width: 100%;

    .roller-group {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 15px;
      padding: 20px;
      background: rgba(255, 255, 255, 0.1);
      border-radius: 15px;
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.2);
      min-width: 300px;

      .code-group {
        display: flex;
        gap: 15px;
        align-items: center;
        justify-content: center;
        perspective: 800px;
        perspective-origin: 50% 50%;
        flex-wrap: wrap;

        ul {
          position: relative;
          width: 60px;
          height: 80px;
          list-style: none;
          margin: 0;
          padding: 0;
          transform-style: preserve-3d;
          transition: transform 2s cubic-bezier(0.25, 0.46, 0.45, 0.94);

          li {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            box-sizing: border-box;
            border: 2px solid #fff;
            background: linear-gradient(145deg, #ffffff, #f0f0f0);
            color: #333;
            font-size: 18px;
            font-weight: bold;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
            border-radius: 6px;
            box-shadow: 0 3px 6px rgba(0, 0, 0, 0.1),
              inset 0 1px 0 rgba(255, 255, 255, 0.8);
            backface-visibility: hidden;
          }
        }
      }

      .button-group {
        display: flex;
        gap: 10px;
        align-items: center;
        justify-content: center;
      }

      .record-display {
        text-align: center;
        margin-top: 10px;

        .record-title {
          color: white;
          font-size: 14px;
          font-weight: 600;
          margin-bottom: 8px;
          text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
        }

        .record-numbers {
          display: flex;
          gap: 6px;
          justify-content: center;
          flex-wrap: wrap;

          .record-number {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 30px;
            height: 30px;
            background: linear-gradient(145deg, #ffd700, #ffed4e);
            border: 1px solid #fff;
            border-radius: 50%;
            font-size: 12px;
            font-weight: bold;
            color: #333;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1),
              inset 0 1px 0 rgba(255, 255, 255, 0.8);
          }
        }
      }
    }

    .global-controls {
      display: flex;
      gap: 20px;
      align-items: center;
      flex-wrap: wrap;
      justify-content: center;
      margin-top: 20px;
      padding: 20px;
      background: rgba(255, 255, 255, 0.05);
      border-radius: 15px;
      backdrop-filter: blur(5px);
      border: 1px solid rgba(255, 255, 255, 0.1);
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .roller-container {
    padding: 20px 10px;
    min-height: 500px;
    max-width: 100%;

    .roller {
      gap: 20px;

      .roller-group {
        min-width: 250px;
        padding: 15px;

        .code-group {
          gap: 10px;

          ul {
            width: 50px;
            height: 70px;

            li {
              font-size: 16px;
            }
          }
        }

        .record-display {
          .record-numbers {
            gap: 4px;

            .record-number {
              width: 25px;
              height: 25px;
              font-size: 11px;
            }
          }
        }
      }

      .global-controls {
        gap: 10px;
        padding: 15px;
        flex-direction: column;

        .el-button {
          width: 100%;
          max-width: 200px;
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .roller-container {
    padding: 15px 5px;

    .roller {
      .roller-group {
        min-width: 200px;
        padding: 10px;

        .code-group {
          gap: 8px;

          ul {
            width: 45px;
            height: 60px;

            li {
              font-size: 14px;
            }
          }
        }
      }
    }
  }
}
</style>
