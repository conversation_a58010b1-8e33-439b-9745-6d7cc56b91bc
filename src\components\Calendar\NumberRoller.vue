<template>
  <div class="roller-container">
    <div class="roller">
      <div class="code-group">
        <ul
          class="code-item"
          v-for="rollerIndex in defaultConfig.rollerCount"
          :key="rollerIndex"
          :style="{ transform: `rotateX(${getRotation(rollerIndex - 1)}deg)` }"
        >
          <li
            v-for="(item, index) in defaultConfig.codeSequenceList"
            :key="index"
            :style="
              getLiTransform(index, defaultConfig.codeSequenceList?.length)
            "
          >
            {{ item }}
          </li>
        </ul>
      </div>
      <div class="result-display">
        <h3>Selected Numbers:</h3>
        <div class="selected-numbers">
          <span
            v-for="(number, index) in selectedNumbers"
            :key="index"
            class="selected-number"
          >
            {{ number }}
          </span>
        </div>
      </div>
      <div class="button-group">
        <el-button @click="rollAll" type="primary" :loading="isRolling">
          {{ isRolling ? "Rolling..." : "Roll All" }}
        </el-button>
        <el-button @click="resetAll" type="default" :disabled="isRolling">
          Reset
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from "vue";
import { initRollerConfig, type RollerConfig } from "./types";
import { getRandomNumber } from "@/utils/RandomUtil";

const props = defineProps<{
  rollerConfig?: Partial<RollerConfig>;
}>();

const defaultConfig = ref<RollerConfig>(
  initRollerConfig(props.rollerConfig ?? {})
);

// 每个滚轮的当前旋转角度
const rotations = reactive<number[]>(
  Array(defaultConfig.value.rollerCount).fill(0)
);

// 滚动状态
const isRolling = ref(false);

// 计算当前选中的数字
const selectedNumbers = computed(() => {
  if (!defaultConfig.value.codeSequenceList?.length) return [];

  const itemCount = defaultConfig.value.codeSequenceList.length;
  const averageDeg = 360 / itemCount;

  return rotations.map((rotation) => {
    // 计算当前旋转角度对应的索引
    const normalizedRotation = ((rotation % 360) + 360) % 360;
    const index = Math.round(normalizedRotation / averageDeg) % itemCount;
    return defaultConfig.value.codeSequenceList![index];
  });
});

// 计算每个 li 元素的 3D 变换
const getLiTransform = (index: number, length: number | undefined) => {
  if (!length) {
    return {};
  }

  const averageDeg = 360 / length;
  const rotateX = index * averageDeg;
  const rotateZ = 160 / 2 / Math.tan((averageDeg / 2 / 180) * Math.PI);
  return {
    transform: `rotateX(-${rotateX}deg) translateZ(${rotateZ}px)`,
  };
};

// 获取滚轮的旋转角度
const getRotation = (rollerIndex: number) => {
  return rotations[rollerIndex] || 0;
};

// 滚动单个滚轮
const rollSingle = (rollerIndex: number) => {
  if (!defaultConfig.value.codeSequenceList?.length) return;

  const itemCount = defaultConfig.value.codeSequenceList.length;
  const averageDeg = 360 / itemCount;
  const randomIndex = getRandomNumber(0, itemCount - 1);
  const targetRotation = randomIndex * averageDeg;

  // 添加多圈旋转效果
  const extraRotations = getRandomNumber(3, 6) * 360;
  rotations[rollerIndex] = targetRotation + extraRotations;
};

// 滚动所有滚轮
const rollAll = async () => {
  if (isRolling.value) return;

  isRolling.value = true;

  // 创建所有滚动的 Promise
  const rollPromises = [];
  for (let i = 0; i < defaultConfig.value.rollerCount!; i++) {
    rollPromises.push(
      new Promise<void>((resolve) => {
        setTimeout(() => {
          rollSingle(i);
          // 等待动画完成
          setTimeout(resolve, 2000);
        }, i * 200);
      })
    );
  }

  // 等待所有滚动完成
  await Promise.all(rollPromises);
  isRolling.value = false;
};

// 重置所有滚轮
const resetAll = () => {
  if (isRolling.value) return;

  rotations.forEach((_, index) => {
    rotations[index] = 0;
  });
};
</script>

<style scoped lang="scss">
.roller-container {
  width: 100%;
  max-width: 800px;
  min-height: 600px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 40px 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);

  .roller {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 40px;
    width: 100%;

    .code-group {
      display: flex;
      gap: 20px;
      align-items: center;
      justify-content: center;
      perspective: 1000px;
      perspective-origin: 50% 50%;
      flex-wrap: wrap;

      ul {
        position: relative;
        width: 80px;
        height: 120px;
        list-style: none;
        margin: 0;
        padding: 0;
        transform-style: preserve-3d;
        transition: transform 2s cubic-bezier(0.25, 0.46, 0.45, 0.94);

        li {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          display: flex;
          justify-content: center;
          align-items: center;
          box-sizing: border-box;
          border: 2px solid #fff;
          background: linear-gradient(145deg, #ffffff, #f0f0f0);
          color: #333;
          font-size: 24px;
          font-weight: bold;
          text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
          border-radius: 8px;
          box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1),
            inset 0 1px 0 rgba(255, 255, 255, 0.8);
          backface-visibility: hidden;
        }
      }
    }

    .result-display {
      text-align: center;
      margin: 20px 0;

      h3 {
        color: white;
        margin-bottom: 15px;
        font-size: 20px;
        text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
      }

      .selected-numbers {
        display: flex;
        gap: 10px;
        justify-content: center;
        flex-wrap: wrap;

        .selected-number {
          display: inline-flex;
          align-items: center;
          justify-content: center;
          width: 50px;
          height: 50px;
          background: linear-gradient(145deg, #ffffff, #f0f0f0);
          border: 2px solid #fff;
          border-radius: 50%;
          font-size: 18px;
          font-weight: bold;
          color: #333;
          box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1),
            inset 0 1px 0 rgba(255, 255, 255, 0.8);
          transition: all 0.3s ease;

          &:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
          }
        }
      }
    }

    .button-group {
      display: flex;
      gap: 15px;
      align-items: center;
      flex-wrap: wrap;
      justify-content: center;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .roller-container {
    padding: 20px 10px;
    min-height: 500px;

    .roller {
      gap: 20px;

      .code-group {
        gap: 10px;

        ul {
          width: 60px;
          height: 90px;

          li {
            font-size: 18px;
          }
        }
      }

      .result-display {
        h3 {
          font-size: 18px;
        }

        .selected-numbers {
          gap: 8px;

          .selected-number {
            width: 40px;
            height: 40px;
            font-size: 16px;
          }
        }
      }

      .button-group {
        gap: 10px;
      }
    }
  }
}
</style>
