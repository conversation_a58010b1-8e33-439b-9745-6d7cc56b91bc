<template>
  <div class="roller-container">
    <div class="roller">
      <div class="code-group">
        <ul
          class="code-item"
          v-for="index in defaultConfig.codeCount"
          :key="index"
        >
          <li
            v-for="(index, item) in defaultConfig.codeSequenceList"
            :style="
              getLiTransform(index, defaultConfig.codeSequenceList?.length)
            "
          >
            {{ item }}
          </li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onBeforeMount } from "vue";
import { initRollerConfig, type RollerConfig } from "./types";
import { transform } from "typescript";

const props = defineProps<{
  rollerConfig?: Partial<RollerConfig>;
}>();

const defaultConfig = ref<RollerConfig>(
  initRollerConfig(props.rollerConfig ?? {})
);
console.log(defaultConfig);

const getLiTransform = (index: number, length: number | undefined) => {
  if (!length) {
    return;
  }

  const averageDeg = 360 / length;
  const rotateX = (index - 1) * averageDeg;
  const rotateZ = 160 / 2 / Math.tan((averageDeg / 2 / 180) * Math.PI);
  return { transform: `rotateX(-${rotateX}deg) translateZ(${rotateZ}px)` };
};
</script>

<style scoped lang="scss">
.roller-container {
  width: 600px;
  height: 900px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;

  background-color: antiquewhite;

  .roller {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    gap: 20px;
    margin-bottom: 30px;
    background-color: aquamarine;

    .code-group {
      display: flex;
      gap: 10px;
      align-items: center;
      perspective: 3000px;
      perspective-origin: 50% 50%;

      ul {
        position: relative;
        min-width: 100px;
        min-height: 160px;
        list-style: none;
        justify-content: center;
        align-items: center;
        transform-style: preserve-3d;

        li {
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          box-sizing: border-box;
          border: 1px solid #3e3e3e;
          background-color: rgba(233, 155, 67, 0.1);
          justify-content: center;
          align-items: center;
          text-align: center;
        }
      }
    }

    .button-group {
      display: flex;
      gap: 10px;
      align-items: center;
    }
  }
}
</style>
