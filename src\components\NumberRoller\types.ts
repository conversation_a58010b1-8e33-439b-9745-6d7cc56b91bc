import { getDaysInNextMonth } from "@/utils/DateUtil";
import { getUniqueRandomNumberList } from "@/utils/RandomUtil";

export interface RollerConfig {

    readonly minCode?: number;
    readonly maxCode?: number;

    codeCount?: number;
    rollerCount?: number;
    codeSequenceList?: number[];
    rollerItemList?: RollerItem[];
}

export interface RollerItem {
    index: number;
    codeList?: number[];
    recordList?: number[];
    isLocked?: boolean;
    currentRotation?: number;
}

export interface ProcessedRollerItem {
    index: number;
    recordList: number[];
}

export function initRollerConfig(config: Partial<RollerConfig>): RollerConfig {
    const minCode: number = 1;
    const maxCode: number = getDaysInNextMonth();
    const codeCount: number = Math.max(config.codeCount ?? 4, 4);
    const rollerCount: number = Math.max(config.rollerCount ?? 3, 3);
    const codeSequenceList: number[] = Array.from({ length: maxCode }, (_, index) => ++index)
    const rollerItemList: RollerItem[] = []

    for (let index = 1; index <= rollerCount; index++) {
        rollerItemList.push({
            index: index,
            codeList: getUniqueRandomNumberList(minCode, maxCode, codeCount),
            recordList: [],
            isLocked: false,
            currentRotation: 0
        })
    }

    return {
        minCode,
        maxCode,
        codeCount,
        rollerCount,
        codeSequenceList,
        rollerItemList
    }
}

