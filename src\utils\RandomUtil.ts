export function getRandomNumber(min : number, max : number) : number {

    min = Math.ceil(min);
    max = Math.floor(max);
    if (min > max) [min, max] = [max, min];

    return Math.floor(Math.random() * (max - min + 1)) + min;
}

export function getUniqueRandomNumberList(min: number, max: number, length: number): number[] {

    const numberList = new Set<number>();
    while (numberList.size < length) {
        numberList.add(getRandomNumber(min,max))
    }

    return Array.from(numberList);
}