<template>
  <div class="calendar-container">
    <header class="calendar-header">
      <h1 class="main-title">Manager Calendar</h1>
      <p class="sub-title">@NullPointer</p>
    </header>

    <section class="user-input-section">
      <label for="username" class="input-label">
        <i class="fas fa-user"></i>
        <span>Gamer</span>
      </label>
      <el-input
        id="username"
        placeholder="Enter your name"
        class="user-input"
      ></el-input>
    </section>

    <div class="time-label">
      <div class="month-info">
        Executing: {{ currentMonth }} Management Game
      </div>
      <div class="count-down">
        Countdown: {{ countDownDays }} days to explosion!
      </div>
    </div>

    <section class="lottery-section">
      <div class="section-title"><i class="fas fa-dice"></i> Lucky Code</div>
      <!-- <NumberRoller /> -->
    </section>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
// import NumberRoller from "../components/orgasmCalendar/NumberRoller.vue";

enum Month {
  January = 0,
  February,
  March,
  April,
  May,
  June,
  July,
  August,
  September,
  October,
  November,
  December,
}

const currentMonth = ref<string>(
  (() => {
    const date = new Date();
    date.setMonth(date.getMonth() + 1);
    return Month[date.getMonth()];
  })()
);

const countDownDays = ref<number>(0);

function updateDaysRemaining() {
  const today = new Date();
  const nextMonth = new Date(today.getFullYear(), today.getMonth() + 1, 1);
  countDownDays.value =
    Math.ceil((nextMonth.getTime() - today.getTime()) / 86_400_000) - 1;
}
onMounted(updateDaysRemaining);
</script>

<style scoped lang="scss">
.calendar-container {
  width: 100%;
  max-width: 1000px;
  min-height: 100vh;
  margin: 0 auto;
  padding: 20px;
  background-color: #7fffd4; // aquamarine的十六进制表示
  display: flex;
  flex-direction: column;
  align-items: center;
}

.calendar-header {
  text-align: center;
  margin-bottom: 2rem;
  padding-top: 1rem;

  .main-title {
    font-size: 2rem;
    font-weight: 800;
    letter-spacing: 1.5px;
    text-transform: uppercase;
    margin-bottom: 0.5rem;
    text-shadow: 2px 2px 0 rgba(0, 0, 0, 0.1);
    font-family: "Arial Rounded MT Bold", "Helvetica Rounded", Arial, sans-serif;
    color: #333;
  }

  .sub-title {
    font-size: 1rem;
    font-weight: 600;
    color: #7f8c8d;
    letter-spacing: 0.5px;
    margin-bottom: 1rem;
  }
}

.user-input-section {
  width: 100%;
  max-width: 400px;

  .input-label {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
    font-weight: 500;
    color: #333;

    i {
      width: 16px;
      text-align: center;
      font-size: 1rem;
    }
  }

  .user-input {
    width: 100%;
  }
}

.time-label {
  margin-top: 25px;
}

.lottery-section {
  margin-top: 25px;
}
</style>
